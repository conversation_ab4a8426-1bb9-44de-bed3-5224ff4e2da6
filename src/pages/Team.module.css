/* Team Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-2);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.subtitle {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Team Grid */
.teamGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .teamGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .teamGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.memberCard {
  text-align: center;
  transform: scale(1);
  transition: transform 200ms ease-in-out;
  cursor: pointer;
  padding: var(--spacing-6);
}

.memberCard:hover {
  transform: scale(1.05);
}

.memberImageContainer {
  position: relative;
}

.memberImage {
  width: 128px;
  height: 128px;
  border-radius: var(--radius-full);
  margin: 0 auto var(--spacing-4);
  box-shadow: var(--shadow-lg);
}

.memberName {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.memberRole {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Modal Styles */
.modalBackdrop {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  animation: backdropFadeIn 0.4s ease-out;
}

.modalOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(12px) saturate(1.8);
  -webkit-backdrop-filter: blur(12px) saturate(1.8);
  cursor: pointer;
  pointer-events: auto;
}

.modal {
  position: relative;
  max-width: 480px;
  width: 100%;
  min-height: 480px;
  margin: var(--spacing-4);
  padding: var(--spacing-10);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  /* Enhanced glassy neumorphic effect */
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(1.8);
  -webkit-backdrop-filter: blur(20px) saturate(1.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  
  /* Spring animation */
  animation: modalSpringIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center center;
}

.modalCloseButton {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-3);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.modalCloseButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modalCloseButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.modalCloseIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.2s ease;
}

.modalCloseButton:hover .modalCloseIcon {
  color: rgba(255, 255, 255, 1);
}

.modalImage {
  width: 140px;
  height: 140px;
  border-radius: var(--radius-full);
  margin-bottom: var(--spacing-6);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 3px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.modalImage:hover {
  transform: scale(1.02);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.modalName {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--spacing-2);
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.025em;
}

.modalRole {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-6);
  text-align: center;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modalDetails {
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  margin-bottom: var(--spacing-8);
  flex: 1;
  line-height: 1.6;
  font-size: var(--font-size-base);
  max-width: 360px;
}

.modalNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
  padding-top: var(--spacing-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.navButton {
  padding: var(--spacing-3);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  min-height: 48px;
}

.navButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.navButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.navIcon {
  height: var(--spacing-6);
  width: var(--spacing-6);
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.2s ease;
}

.navButton:hover .navIcon {
  color: rgba(255, 255, 255, 1);
}

/* Contact Icons */
.contactIcons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  flex-wrap: wrap;
  max-width: 200px;
}

.contactButton {
  padding: var(--spacing-3);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 44px;
  min-height: 44px;
}

.contactButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.contactButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.6);
  outline-offset: 2px;
}

.contactButton:active {
  transform: translateY(0) scale(0.98);
}

.contactIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
}

.contactButton:hover .contactIcon {
  color: rgba(255, 255, 255, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Animations */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSpringIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  60% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .teamGrid {
    gap: var(--spacing-4);
  }
  
  .memberCard {
    padding: var(--spacing-4);
  }
  
  .memberImage {
    width: 96px;
    height: 96px;
  }
  
  .modal {
    margin: var(--spacing-3);
    padding: var(--spacing-8);
    min-height: 400px;
    max-width: calc(100vw - var(--spacing-6));
  }
  
  .modalImage {
    width: 110px;
    height: 110px;
    margin-bottom: var(--spacing-4);
  }
  
  .modalName {
    font-size: var(--font-size-2xl);
  }
  
  .modalRole {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-4);
  }
  
  .modalDetails {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-6);
  }
  
  .contactIcons {
    gap: var(--spacing-1);
    max-width: 180px;
  }
  
  .contactButton {
    min-width: 40px;
    min-height: 40px;
    padding: var(--spacing-2);
  }
  
  .contactIcon {
    height: var(--spacing-4);
    width: var(--spacing-4);
  }
  
  .navButton {
    min-width: 44px;
    min-height: 44px;
  }
  
  .navIcon {
    height: var(--spacing-5);
    width: var(--spacing-5);
  }
}

@media (max-width: 480px) {
  .modal {
    margin: var(--spacing-2);
    padding: var(--spacing-6);
    min-height: 360px;
  }
  
  .modalImage {
    width: 100px;
    height: 100px;
  }
  
  .modalName {
    font-size: var(--font-size-xl);
  }
  
  .contactIcons {
    max-width: 160px;
  }
}
